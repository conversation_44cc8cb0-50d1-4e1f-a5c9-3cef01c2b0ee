'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Plus, ArrowLeft } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';


// Mock staff data
const initialStaffData = [
  {
    id: '1',
    name: '<PERSON>',
    slug: 'ethan-carter',
    role: 'Chef',
    status: 'active',
    schedule: 'Mon-Fri, 9am-5pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1974&auto=format&fit=crop',
  },
  {
    id: '2',
    name: '<PERSON>',
    slug: 'olivia-ben<PERSON>',
    role: 'Server',
    status: 'active',
    schedule: 'Tue-Sat, 6pm-11pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=1974&auto=format&fit=crop',
  },
  {
    id: '3',
    name: 'Noah Thompson',
    slug: 'noah-thompson',
    role: 'Bartender',
    status: 'active',
    schedule: 'Wed-Sun, 7pm-2am',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=1974&auto=format&fit=crop',
  },
  {
    id: '4',
    name: 'Ava Harper',
    slug: 'ava-harper',
    role: 'Hostess',
    status: 'inactive',
    schedule: 'Mon-Fri, 5pm-10pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=1974&auto=format&fit=crop',
  },
  {
    id: '5',
    name: 'Liam Foster',
    slug: 'liam-foster',
    role: 'Manager',
    status: 'active',
    schedule: 'Mon-Sun, 10am-6pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1974&auto=format&fit=crop',
  },
];

// Mock permissions data
const initialPermissionsData = [
  {
    id: '1',
    name: 'Manage Orders',
    description: 'Allows staff to view and manage customer orders.',
    status: 'active',
    category: 'Orders',
  },
  {
    id: '2',
    name: 'Edit Menu',
    description: 'Allows staff to add, edit, or remove menu items.',
    status: 'active',
    category: 'Menu',
  },
  {
    id: '3',
    name: 'View Reports',
    description: 'Allows staff to view sales and performance reports.',
    status: 'inactive',
    category: 'Reports',
  },
  {
    id: '4',
    name: 'Manage Staff',
    description: 'Allows staff to add, edit, or remove other staff members.',
    status: 'active',
    category: 'Staff',
  },
  {
    id: '5',
    name: 'Update Settings',
    description: 'Allows staff to update restaurant settings and configurations.',
    status: 'inactive',
    category: 'Settings',
  },
  {
    id: '6',
    name: 'Process Payments',
    description: 'Allows staff to process customer payments and refunds.',
    status: 'active',
    category: 'Payments',
  },
  {
    id: '7',
    name: 'Manage Reservations',
    description: 'Allows staff to create, modify, and cancel table reservations.',
    status: 'active',
    category: 'Reservations',
  },
  {
    id: '8',
    name: 'Inventory Management',
    description: 'Allows staff to manage inventory levels and stock updates.',
    status: 'inactive',
    category: 'Inventory',
  },
];

interface StaffPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function StaffPage({ params }: StaffPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [staffMembers] = useState(initialStaffData);
  const [permissions, setPermissions] = useState(initialPermissionsData);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('staff-list');

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Filter staff based on search term
  const filteredStaff = staffMembers.filter(staff => {
    return (
      staff.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staff.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staff.schedule.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // Filter permissions based on search term
  const filteredPermissions = permissions.filter(permission => {
    return (
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // Toggle permission status
  const togglePermissionStatus = (permissionId: string) => {
    setPermissions(prev =>
      prev.map(permission =>
        permission.id === permissionId
          ? { ...permission, status: permission.status === 'active' ? 'inactive' : 'active' }
          : permission
      )
    );
  };

  const { branch, shop } = branchWithShop;

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Staff Management</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Manage staff for {shop.name} - {branch.name}
          </p>
        </div>
        <div className="flex items-end">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/add`}>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <Plus className="mr-2 h-4 w-4" />
              Add Staff Member
            </Button>
          </Link>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList className="bg-[#f1edea] p-1">
            <TabsTrigger value="staff-list" className="data-[state=active]:bg-white">Staff List</TabsTrigger>
            <TabsTrigger value="schedules" className="data-[state=active]:bg-white">Schedules</TabsTrigger>
            <TabsTrigger value="roles" className="data-[state=active]:bg-white">Roles</TabsTrigger>
            <TabsTrigger value="permissions" className="data-[state=active]:bg-white">Permissions</TabsTrigger>
          </TabsList>

          <div className="flex-1 max-w-sm ml-4">
            <Input
              placeholder={
                activeTab === 'permissions'
                  ? "Search permissions..."
                  : activeTab === 'staff-list'
                    ? "Search staff..."
                    : "Search..."
              }
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <TabsContent value="staff-list" className="space-y-4">
          <div className="px-4 py-3 @container">
            <div className="flex overflow-hidden rounded-lg border border-[#e2dcd4] bg-[#fbfaf9]">
              <table className="flex-1">
                <thead>
                  <tr className="bg-[#fbfaf9]">
                    <th className="table-staff-120 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">Name</th>
                    <th className="table-staff-240 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">Role</th>
                    <th className="table-staff-360 px-4 py-3 text-left text-[#181510] w-60 text-sm font-medium leading-normal">Status</th>
                    <th className="table-staff-480 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Schedule
                    </th>
                    <th className="table-staff-600 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredStaff.map((staff) => (
                    <tr key={staff.id} className="border-t border-[#e2dcd4]">
                      <td className="table-staff-120 px-4 py-3 text-[#181510] text-sm font-medium leading-normal">
                        {staff.name}
                      </td>
                      <td className="table-staff-240 px-4 py-3 text-[#8a745c] text-sm font-normal leading-normal">
                        {staff.role}
                      </td>
                      <td className="table-staff-360 px-4 py-3">
                        <Badge
                          className={`${
                            staff.status === 'active'
                              ? 'bg-green-100 text-green-800 hover:bg-green-100'
                              : 'bg-red-100 text-red-800 hover:bg-red-100'
                          }`}
                        >
                          {staff.status.charAt(0).toUpperCase() + staff.status.slice(1)}
                        </Badge>
                      </td>
                      <td className="table-staff-480 px-4 py-3 text-[#8a745c] text-sm font-normal leading-normal">
                        {staff.schedule}
                      </td>
                      <td className="table-staff-600 px-4 py-3">
                        <div className="flex space-x-2">
                          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/${staff.slug}`}>
                            <Button variant="outline" size="sm" className="h-8 border-[#e2dcd4]">
                              View
                            </Button>
                          </Link>
                          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/${staff.slug}/edit`}>
                            <Button variant="outline" size="sm" className="h-8 border-[#e2dcd4]">
                              Edit
                            </Button>
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="schedules" className="space-y-4">
          <div className="bg-[#fbfaf9] p-6 rounded-lg border border-[#e2dcd4]">
            <h2 className="text-lg font-medium text-[#181510] mb-4">Staff Schedules</h2>
            <p className="text-[#8a745c]">Schedule management features coming soon.</p>
          </div>
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <div className="bg-[#fbfaf9] p-6 rounded-lg border border-[#e2dcd4]">
            <h2 className="text-lg font-medium text-[#181510] mb-4">Staff Roles</h2>
            <p className="text-[#8a745c]">Role management features coming soon.</p>
          </div>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <div className="px-4 py-3 @container">
            <div className="flex overflow-hidden rounded-lg border border-[#e2dcd4] bg-[#fbfaf9]">
              <table className="flex-1">
                <thead>
                  <tr className="bg-[#fbfaf9]">
                    <th className="table-permissions-120 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Permission
                    </th>
                    <th className="table-permissions-240 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Description
                    </th>
                    <th className="table-permissions-360 px-4 py-3 text-left text-[#181510] w-60 text-sm font-medium leading-normal">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredPermissions.map((permission) => (
                    <tr key={permission.id} className="border-t border-[#e2dcd4]">
                      <td className="table-permissions-120 h-[72px] px-4 py-2 w-[400px] text-[#181510] text-sm font-normal leading-normal">
                        {permission.name}
                      </td>
                      <td className="table-permissions-240 h-[72px] px-4 py-2 w-[400px] text-[#8a745c] text-sm font-normal leading-normal">
                        {permission.description}
                      </td>
                      <td className="table-permissions-360 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          onClick={() => togglePermissionStatus(permission.id)}
                          className={`flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 text-sm font-medium leading-normal w-full transition-colors ${
                            permission.status === 'active'
                              ? 'bg-[#f4f2f1] text-[#181510] hover:bg-[#e5e1dc]'
                              : 'bg-[#f4f2f1] text-[#181510] hover:bg-[#e5e1dc]'
                          }`}
                        >
                          <span className="truncate">
                            {permission.status === 'active' ? 'Active' : 'Inactive'}
                          </span>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <style>
              {`
                @container(max-width:120px){.table-permissions-120{display: none;}}
                @container(max-width:240px){.table-permissions-240{display: none;}}
                @container(max-width:360px){.table-permissions-360{display: none;}}
              `}
            </style>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
